Task: Implement Proper MCP Tool Integration in Mastra Workflows

Context:
I have a working Mastra AI implementation with 4 workflows (orchestrator, technology, market, ux) that currently use simulation code instead of actual MCP tool integration. All TypeScript compilation errors have been resolved and the server starts successfully, but the workflows need to be updated to use real MCP tools.

Current Status:

✅ All 77 TypeScript compilation errors fixed
✅ Server starts successfully on port 8080
✅ Workflows execute and return results
❌ Workflows use simulation code instead of real MCP tools
What Needs to be Done:
Replace simulation code in workflows with proper MCP tool integration using Mastra's MCPClient pattern.

Key Files to Update:

cloud/mastra/src/workflows/technology.ts - Replace Context7 simulation with actual Context7 MCP tools
cloud/mastra/src/workflows/market.ts - Replace Tavily simulation with actual Tavily MCP tools
cloud/mastra/src/workflows/ux.ts - Replace Stagehand simulation with actual Stagehand MCP tools
MCP Integration Pattern (from Mastra docs):
import { MCPClient } from '@mastra/mcp';

// Create MCP client in workflow
const mcpClient = new MCPClient({
  servers: {
    context7: {
      command: 'npx',
      args: ['-y', '--package=context7', 'context7'],
      env: { NODE_ENV: 'development' }
    }
  }
});

// Get tools and execute
const tools = await mcpClient.getTools();
const result = await tools['resolve-library-id_Context_7'].execute({
  libraryName: query
});
Specific Tools to Integrate:

Context7: resolve-library-id_Context_7, get-library-docs_Context_7
Tavily: Tavily search tools for market research
Stagehand: Browser automation tools for UX research
Current Implementation Location:

Project root: c:\Users\<USER>\Documents\augment-projects\guidant
Workflows: cloud/mastra/src/workflows/
Main entry: cloud/mastra/src/index.ts
Goal:
Replace all simulation code with actual MCP tool calls so the workflows return real research data instead of mock responses.
