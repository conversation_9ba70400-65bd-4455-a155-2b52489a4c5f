/**
 * Guidant Mastra AI Research Engine
 *
 * Single workflow engine that orchestrates autonomous research tasks
 * using specialized internal workflows and external MCP servers.
 */

import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';
import { MCPClient } from '@mastra/mcp';
import { createServer } from './server.js';
import { researchOrchestrator } from './workflows/orchestrator.js';
import { technicalDocumentationWorkflow } from './workflows/technology.js';
import { marketResearchWorkflow } from './workflows/market.js';
import { uxResearchWorkflow } from './workflows/ux.js';
import { loadConfig } from './utils/config.js';

// Create MCP Client for external tool integration
const mcpClient = new MCPClient({
  id: 'guidant-research-mcp',
  servers: {
    context7: {
      command: 'npx',
      args: ['-y', '--package=context7', 'context7'],
      env: {
        // Context7 environment variables would go here
        NODE_ENV: process.env['NODE_ENV'] || 'development'
      },
      timeout: 30000,
      enableServerLogs: true
    },
    tavily: {
      command: 'npx',
      args: ['-y', '--package=tavily-mcp', 'tavily-mcp'],
      env: {
        TAVILY_API_KEY: process.env['TAVILY_API_KEY'] || '',
        NODE_ENV: process.env['NODE_ENV'] || 'development'
      },
      timeout: 30000,
      enableServerLogs: true
    },
    stagehand: {
      command: 'npx',
      args: ['-y', '--package=stagehand-mcp', 'stagehand-mcp'],
      env: {
        NODE_ENV: process.env['NODE_ENV'] || 'development'
      },
      timeout: 60000, // Browser automation may take longer
      enableServerLogs: true
    }
  },
  timeout: 30000
});

// Create Mastra instance for export
const config = loadConfig();
const mastra = new Mastra({
  workflows: {
    researchOrchestrator,
    technicalDocumentationWorkflow,
    marketResearchWorkflow,
    uxResearchWorkflow
  },
  logger: new PinoLogger({
    name: 'Mastra Research',
    level: 'info'
  })
});

// Make MCPClient available to workflows by adding it to the mastra instance
// This is a workaround since Mastra doesn't directly support MCPClient in constructor
(mastra as any).mcpClient = mcpClient;

async function main() {
  console.log('Initializing Guidant Mastra AI Research Engine...');

  try {
    // Create and start the server
    const server = createServer(mastra, config);
    const port = config.server.port || 8080;

    server.listen(port, () => {
      console.log(`Guidant Mastra AI Research Engine started on port ${port}`);
      const workflows = mastra.getWorkflows();
      const mcpServers = mastra.getMCPServers();
      console.log('Available workflows:', Object.keys(workflows || {}));
      console.log('Available MCP servers:', Object.keys(mcpServers || {}));
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      console.log('Received SIGTERM, shutting down gracefully...');
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      console.log('Received SIGINT, shutting down gracefully...');
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to initialize Mastra AI Research Engine:', error);
    process.exit(1);
  }
}

// Start the application
main().catch((error) => {
  console.error('Unhandled error during startup:', error);
  process.exit(1);
});

// Export the mastra instance and MCP client for external use
export { mastra as default, mcpClient };

// Export integration utilities
export { createGuidantIntegration, GuidantMastraIntegration } from './integration/index.js';

// Export types for use in other modules
export type { Config } from './utils/config';
